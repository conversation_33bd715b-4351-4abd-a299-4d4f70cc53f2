---
description: vue frontend rule
globs: 
alwaysApply: false
---
# Vue3 前端开发规范

## 核心原则

你是一位Vue3前端开发专家。严格遵循以下开发原则：

### 技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **UI组件**: Element Plus / Ant Design Vue
- **CSS预处理**: SCSS
- **代码规范**: ESLint + Prettier
- **包管理**: npm

### 开发哲学
1. **类型安全优先** - 所有代码必须有完整的TypeScript类型定义
2. **组合式API优先** - 使用Composition API而非Options API
3. **模块化设计** - 按功能模块组织代码结构
4. **性能优化** - 代码分割、懒加载、缓存优化
5. **可维护性** - 清晰的命名、完整的注释、统一的风格

## 中文注释规范

### 注释原则
- **必须使用中文注释** - 所有注释必须使用简体中文
- **注释要简洁明了** - 避免冗长的描述，直接说明用途和功能
- **关键逻辑必须注释** - 复杂的业务逻辑、算法、正则表达式等必须添加注释
- **接口和类型必须注释** - 所有公共接口、类型定义都要有JSDoc注释

### 文件头注释
```typescript
/**
 * 用户管理相关API接口
 * @description 包含用户的增删改查、权限管理等功能
 * <AUTHOR>
 * @date 2024-01-15
 * @version 1.0.0
 */
```

### 函数注释
```typescript
/**
 * 获取用户列表
 * @param query 查询参数
 * @param query.page 页码，从1开始
 * @param query.pageSize 每页数量，默认10
 * @param query.keyword 搜索关键词，可选
 * @returns 返回用户列表和分页信息
 * @throws 当参数无效时抛出错误
 * @example
 * ```typescript
 * const result = await getUserList({ page: 1, pageSize: 20 })
 * console.log(result.data) // 用户列表
 * ```
 */
async function getUserList(query: UserListQuery): Promise<ApiResponse<UserListResult>> {
  // 参数验证
  if (query.page < 1) {
    throw new Error('页码必须大于0')
  }
  
  // 发送请求
  return request.get('/api/users', { params: query })
}
```

### 接口和类型注释
```typescript
/**
 * 用户信息接口
 */
export interface User {
  /** 用户ID */
  id: number
  /** 用户名 */
  name: string
  /** 邮箱地址 */
  email: string
  /** 头像URL，可选 */
  avatar?: string
  /** 用户角色 */
  role: UserRole
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 用户角色枚举
 * - admin: 管理员，拥有所有权限
 * - user: 普通用户，基础权限
 * - guest: 访客，只读权限
 */
export type UserRole = 'admin' | 'user' | 'guest'
```

### 组件注释
```vue
<template>
  <!-- 用户资料卡片组件 -->
  <div class="user-profile">
    <!-- 头部信息区域 -->
    <header class="user-profile__header">
      <h1>{{ user.name }}</h1>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="user-profile__content">
      <!-- 用户详细信息 -->
      <div class="user-info">
        <p>邮箱：{{ user.email }}</p>
        <p>角色：{{ roleText }}</p>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
/**
 * 用户资料组件
 * @description 展示用户的基本信息和操作按钮
 */

// 组件属性定义
interface Props {
  /** 用户ID */
  userId: number
  /** 是否显示操作按钮 */
  showActions?: boolean
}

// 响应式数据
const loading = ref(false) // 加载状态
const user = ref<User | null>(null) // 用户信息

// 计算属性 - 角色显示文本
const roleText = computed(() => {
  const roleMap = {
    admin: '管理员',
    user: '普通用户',
    guest: '访客'
  }
  return user.value ? roleMap[user.value.role] : '未知'
})

/**
 * 获取用户信息
 * @description 根据用户ID获取用户详细信息
 */
const fetchUser = async () => {
  try {
    loading.value = true
    // 调用API获取用户信息
    const data = await userApi.getById(props.userId)
    user.value = data
  } catch (error) {
    // 错误处理
    ElMessage.error('获取用户信息失败')
    console.error('获取用户失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 复杂逻辑注释
```typescript
/**
 * 权限检查函数
 * @param userRole 用户角色
 * @param requiredPermission 需要的权限
 * @returns 是否有权限
 */
function checkPermission(userRole: UserRole, requiredPermission: string): boolean {
  // 权限级别映射表
  const roleLevel = {
    guest: 1,   // 访客权限最低
    user: 2,    // 普通用户
    admin: 3    // 管理员权限最高
  }
  
  // 权限要求映射表
  const permissionLevel = {
    'read': 1,      // 读取权限
    'write': 2,     // 写入权限
    'delete': 3,    // 删除权限
    'admin': 3      // 管理权限
  }
  
  // 检查用户角色级别是否满足权限要求
  return roleLevel[userRole] >= (permissionLevel[requiredPermission] || 0)
}
```

### 常量和配置注释
```typescript
/**
 * API配置常量
 */
export const API_CONFIG = {
  /** API基础URL */
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  /** 请求超时时间（毫秒） */
  TIMEOUT: 10000,
  /** 重试次数 */
  RETRY_COUNT: 3
} as const

/**
 * 用户状态枚举
 */
export const USER_STATUS = {
  /** 激活状态 */
  ACTIVE: 1,
  /** 禁用状态 */
  DISABLED: 0,
  /** 待审核状态 */
  PENDING: 2
} as const
```

### 注释最佳实践
1. **避免无意义注释** - 不要为显而易见的代码添加注释
2. **及时更新注释** - 代码修改时同步更新相关注释
3. **使用TODO标记** - 临时代码或待完善功能使用TODO标记
4. **统一注释风格** - 团队内保持注释格式一致

```typescript
// ❌ 不好的注释
const count = 0 // 定义count变量

// ✅ 好的注释
const retryCount = 0 // 请求失败重试次数，最大3次

// TODO: 优化用户权限检查逻辑，考虑缓存机制
// FIXME: 修复用户登录状态异常问题
// NOTE: 此处使用防抖处理，避免频繁请求
```

## 项目结构

```
src/
├── api/                    # API接口层
│   ├── modules/           # 按业务模块分类
│   │   ├── auth.ts       # 认证相关API
│   │   └── user.ts       # 用户相关API
│   ├── types.ts          # API类型定义
│   └── index.ts          # API统一导出
├── assets/                # 静态资源
│   ├── icons/            # SVG图标
│   ├── images/           # 图片资源
│   └── styles/           # 全局样式
├── components/            # 公共组件
│   ├── Base/             # 基础组件
│   ├── Business/         # 业务组件
│   └── Layout/           # 布局组件
├── composables/           # 组合式函数
│   ├── useAuth.ts        # 认证逻辑
│   └── useRequest.ts     # 请求逻辑
├── constants/             # 常量定义
├── directives/            # 自定义指令
├── layouts/               # 页面布局
├── plugins/               # 插件配置
├── router/                # 路由配置
│   ├── guards/           # 路由守卫
│   └── modules/          # 路由模块
├── stores/                # 状态管理
│   └── modules/          # 状态模块
├── types/                 # 类型定义
├── utils/                 # 工具函数
├── views/                 # 页面组件
├── App.vue               # 根组件
└── main.ts               # 应用入口
```

## 组件开发规范

### 组件文件结构
```vue
<template>
  <!-- 使用语义化HTML标签 -->
  <div class="user-profile">
    <header class="user-profile__header">
      <h1>{{ user.name }}</h1>
    </header>
    
    <main class="user-profile__content">
      <!-- 内容区域 -->
    </main>
  </div>
</template>

<script setup lang="ts">
// 1. 框架导入
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 第三方库导入
import { ElMessage } from 'element-plus'

// 3. 项目内导入
import { userApi } from '@/api'
import type { User } from '@/types'

// 4. 类型定义
interface Props {
  userId: number
  showActions?: boolean
}

// 5. Props定义
const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

// 6. Emits定义
const emit = defineEmits<{
  userUpdate: [user: User]
  actionClick: [action: string]
}>()

// 7. 响应式状态
const loading = ref(false)
const user = ref<User | null>(null)

// 8. 计算属性
const displayName = computed(() => {
  return user.value?.name || '未知用户'
})

// 9. 方法定义
const fetchUser = async () => {
  try {
    loading.value = true
    const data = await userApi.getById(props.userId)
    user.value = data
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 10. 生命周期
onMounted(() => {
  fetchUser()
})
</script>

<style scoped lang="scss">
.user-profile {
  &__header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  &__content {
    padding: 16px;
  }
}
</style>
```

### 组件命名规范
- **组件名**: PascalCase，至少两个单词 (UserProfile.vue)
- **Props**: camelCase (userName, showAvatar)
- **Events**: kebab-case (user-update, action-click)
- **CSS类名**: BEM命名法 (user-profile__header)

## TypeScript 使用规范

### 类型定义
```typescript
// types/user.ts
export interface User {
  id: number
  name: string
  email: string
  avatar?: string
  role: UserRole
  createdAt: string
  updatedAt: string
}

export type UserRole = 'admin' | 'user' | 'guest'

export interface CreateUserRequest {
  name: string
  email: string
  password: string
  role?: UserRole
}

export interface UserListQuery {
  page: number
  pageSize: number
  keyword?: string
  role?: UserRole
}
```

### API类型定义
```typescript
// types/api.ts
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

export interface ApiError {
  code: number
  message: string
  details?: Record<string, any>
}
```

## 状态管理规范 (Pinia)

### Store定义
```typescript
// stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm } from '@/types'
import { authApi } from '@/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref('')
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => user.value?.role || 'guest')
  
  // 权限检查
  const hasPermission = (permission: string) => {
    return user.value?.permissions?.includes(permission) ?? false
  }
  
  // 动作
  const login = async (form: LoginForm) => {
    const response = await authApi.login(form)
    token.value = response.token
    user.value = response.user
    
    // 存储到localStorage
    localStorage.setItem('token', response.token)
    localStorage.setItem('user', JSON.stringify(response.user))
    
    return response
  }
  
  const logout = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }
  
  // 初始化
  const init = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
    }
  }
  
  return {
    user,
    token,
    isLoggedIn,
    userRole,
    hasPermission,
    login,
    logout,
    init
  }
})
```

## API层设计

### HTTP请求封装
```typescript
// utils/request.ts
import axios from 'axios'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores'
import type { ApiResponse } from '@/types'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { code, data, message } = response.data
    
    if (code === 200) {
      return data
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    const { response } = error
    
    if (response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      window.location.href = '/login'
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    
    return Promise.reject(error)
  }
)

export default request
```

### API模块定义
```typescript
// api/modules/user.ts
import request from '@/utils/request'
import type { 
  User, 
  CreateUserRequest, 
  UserListQuery,
  PaginatedResponse 
} from '@/types'

export const userApi = {
  // 获取用户列表
  getList: (params: UserListQuery) =>
    request.get<PaginatedResponse<User>>('/users', { params }),
  
  // 获取用户详情
  getById: (id: number) =>
    request.get<User>(`/users/${id}`),
  
  // 创建用户
  create: (data: CreateUserRequest) =>
    request.post<User>('/users', data),
  
  // 更新用户
  update: (id: number, data: Partial<User>) =>
    request.put<User>(`/users/${id}`, data),
  
  // 删除用户
  delete: (id: number) =>
    request.delete(`/users/${id}`),
  
  // 批量删除
  batchDelete: (ids: number[]) =>
    request.delete('/users/batch', { data: { ids } })
}
```

## 路由规范

### 路由定义
```typescript
// router/modules/user.ts
import type { RouteRecordRaw } from 'vue-router'

export const userRoutes: RouteRecordRaw[] = [
  {
    path: '/users',
    name: 'UserManagement',
    component: () => import('@/layouts/BasicLayout.vue'),
    meta: {
      title: '用户管理',
      icon: 'users',
      requiresAuth: true,
      permissions: ['user:read']
    },
    children: [
      {
        path: '',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户列表',
          keepAlive: true
        }
      },
      {
        path: 'create',
        name: 'UserCreate',
        component: () => import('@/views/user/UserForm.vue'),
        meta: {
          title: '创建用户',
          permissions: ['user:create']
        }
      },
      {
        path: ':id/edit',
        name: 'UserEdit',
        component: () => import('@/views/user/UserForm.vue'),
        meta: {
          title: '编辑用户',
          permissions: ['user:update']
        }
      }
    ]
  }
]
```

### 路由守卫
```typescript
// router/guards/auth.ts
import type { Router } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores'

export function setupAuthGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 检查登录状态
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      next('/login')
      return
    }
    
    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = to.meta.permissions.some(
        (permission: string) => authStore.hasPermission(permission)
      )
      
      if (!hasPermission) {
        ElMessage.error('权限不足')
        next('/403')
        return
      }
    }
    
    next()
  })
}
```

## 样式规范

### SCSS变量和混入
```scss
// assets/styles/variables.scss
// 主题色彩
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 中性色彩
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 边框色彩
$border-base: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;
$border-extra-light: #f2f6fc;

// 尺寸变量
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
```

```scss
// assets/styles/mixins.scss
// Flex布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multi($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}
```

## 工具配置

### ESLint配置
```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true
  },
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/typescript/recommended',
    '@vue/prettier'
  ],
  parserOptions: {
    ecmaVersion: 2021,
    parser: '@typescript-eslint/parser',
    sourceType: 'module'
  },
  rules: {
    // Vue规则
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/no-unused-vars': 'error',
    'vue/no-unused-components': 'warn',
    'vue/require-default-prop': 'off',
    'vue/multi-word-component-names': 'off',
    
    // TypeScript规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/ban-ts-comment': 'warn',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error'
  }
}
```

### Prettier配置
```javascript
// .prettierrc.js
module.exports = {
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  semi: false,
  singleQuote: true,
  quoteProps: 'as-needed',
  trailingComma: 'none',
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'avoid',
  endOfLine: 'lf',
  vueIndentScriptAndStyle: false
}
```

### Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

## 性能优化

### 代码分割
```typescript
// 路由懒加载
const UserList = () => import('@/views/user/UserList.vue')

// 组件懒加载
import { defineAsyncComponent } from 'vue'
const HeavyComponent = defineAsyncComponent(
  () => import('@/components/HeavyComponent.vue')
)

// 条件加载
const AdminPanel = defineAsyncComponent({
  loader: () => import('@/components/AdminPanel.vue'),
  loadingComponent: () => import('@/components/Loading.vue'),
  errorComponent: () => import('@/components/Error.vue'),
  delay: 200,
  timeout: 3000
})
```

### 图片优化
```vue
<template>
  <!-- WebP格式优先 -->
  <picture>
    <source :srcset="webpSrc" type="image/webp">
    <img :src="jpegSrc" :alt="alt" loading="lazy">
  </picture>
  
  <!-- 响应式图片 -->
  <img 
    :srcset="`${src}?w=400 400w, ${src}?w=800 800w`"
    sizes="(max-width: 768px) 400px, 800px"
    :src="src"
    :alt="alt"
  >
</template>
```

## 错误处理

### 全局错误处理
```typescript
// plugins/errorHandler.ts
import type { App } from 'vue'
import { ElMessage } from 'element-plus'

export function setupErrorHandler(app: App) {
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err)
    console.error('Component:', vm)
    console.error('Error Info:', info)
    
    ElMessage.error('应用发生错误，请刷新页面重试')
  }
  
  // 全局未捕获的Promise错误
  window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled Promise Rejection:', event.reason)
    ElMessage.error('请求失败，请稍后重试')
    event.preventDefault()
  })
}
```

## 测试规范

### 组件测试
```typescript
// tests/components/UserCard.spec.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  const mockUser = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '/avatar.jpg'
  }
  
  it('renders user information correctly', () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    
    expect(wrapper.text()).toContain('John Doe')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
  
  it('emits user-click event when clicked', async () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('user-click')).toBeTruthy()
    expect(wrapper.emitted('user-click')?.[0]).toEqual([mockUser])
  })
})
```

## 部署配置

### 环境变量
```bash
# .env
VITE_APP_TITLE=Vue Service
VITE_APP_VERSION=1.0.0

# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_ENABLE_MOCK=true

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_ENABLE_MOCK=false
```

### Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 开发工作流

### Git提交规范
```bash
# 类型(范围): 描述
feat(auth): 添加用户登录功能
fix(api): 修复用户列表分页问题
docs(readme): 更新安装说明
style(button): 调整按钮样式
refactor(utils): 重构日期工具函数
perf(list): 优化列表渲染性能
test(user): 添加用户组件测试
chore(deps): 升级依赖版本
```

### 代码审查清单
- [ ] 代码符合ESLint规范
- [ ] TypeScript类型定义完整
- [ ] 组件命名遵循规范
- [ ] 错误处理完善
- [ ] 性能优化考虑
- [ ] 安全性检查
- [ ] 测试覆盖充分
- [ ] 文档更新及时

---

**版本**: v2.0.0  
**更新时间**: 2024年12月

此规范参考cursor.directory最佳实践，为Vue3项目提供完整的开发指南。




